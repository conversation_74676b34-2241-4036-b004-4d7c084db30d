import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  /* config options here */
  serverExternalPackages: ['@prisma/client'],

  // Optimize for Netlify deployment
  experimental: {
    // Reduce bundle size
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // Configure webpack for Netlify compatibility
  webpack: (config, { isServer }) => {
    // Fix for Node.js globals in server environment
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }

    return config
  },

  // Headers configuration for large file uploads
  async headers() {
    return [
      {
        source: '/api/app/:path*/upload',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'POST, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },

  // Configure body size limits for large file uploads
  serverRuntimeConfig: {
    maxUploadSize: 100 * 1024 * 1024, // 100MB
  },
};

export default withNextIntl(nextConfig);
