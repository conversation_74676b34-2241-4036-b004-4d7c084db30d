import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  /* config options here */
  serverExternalPackages: ['@prisma/client'],

  // Optimize for Cloudflare Pages deployment
  experimental: {
    // Reduce bundle size
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // Configure webpack for Cloudflare Pages compatibility
  webpack: (config, { isServer }) => {
    // Fix for "self is not defined" error in Cloudflare Pages
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }

    return config
  },

  // Headers configuration
  async headers() {
    return []
  },

  // Configure body size limits
  serverRuntimeConfig: {
    maxUploadSize: 100 * 1024 * 1024, // 100MB
  },
};

export default withNextIntl(nextConfig);
