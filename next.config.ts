import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  /* config options here */
  serverExternalPackages: ['@prisma/client'],

  // Optimize for Cloudflare Pages deployment
  experimental: {
    // Reduce bundle size
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // Configure webpack for better optimization and Cloudflare compatibility
  webpack: (config, { isServer, dev }) => {
    // Fix for "self is not defined" error in Cloudflare Pages
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }

    // Add global polyfills for browser APIs - more comprehensive approach
    config.plugins = config.plugins || []
    config.plugins.push(
      new config.webpack.DefinePlugin({
        'typeof window': JSON.stringify('object'),
        'typeof self': JSON.stringify('object'),
        'typeof global': JSON.stringify('object'),
      })
    )

    // Add a banner to inject polyfills at the top of every chunk
    config.plugins.push(
      new config.webpack.BannerPlugin({
        banner: `
          if (typeof self === 'undefined') {
            var self = this || {};
          }
          if (typeof window === 'undefined') {
            var window = this || {};
          }
          if (typeof global === 'undefined') {
            var global = this || {};
          }
        `,
        raw: true,
        entryOnly: false,
      })
    )

    // Optimize bundle size for production
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              maxSize: 244000, // 244KB chunks
            },
          },
        },
      }
    }

    return config
  },

  // Headers configuration
  async headers() {
    return []
  },

  // Configure body size limits
  serverRuntimeConfig: {
    maxUploadSize: 100 * 1024 * 1024, // 100MB
  },
};

export default withNextIntl(nextConfig);
