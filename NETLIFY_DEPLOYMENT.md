# Netlify Deployment Guide

## Quick Setup

### 1. Deploy to Netlify

1. Go to [netlify.com](https://netlify.com) and sign up/login
2. Click "Add new site" → "Import an existing project"
3. Connect your GitHub account
4. Select your `avedevportal` repository
5. Build settings are automatically detected from `netlify.toml`
6. Click "Deploy site"

### 2. Environment Variables

Add these environment variables in Netlify dashboard (Site settings → Environment variables):

```bash
# Database
DATABASE_URL=your_mongodb_connection_string

# NextAuth
NEXTAUTH_URL=https://your-app-name.netlify.app
NEXTAUTH_SECRET=your_nextauth_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Netlify Blobs Storage
NETLIFY_SITE_ID=your_netlify_site_id
NETLIFY_TOKEN=your_netlify_token

# API Key for external access
API_KEY=your_api_key

# Email Configuration (optional)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
```

### 3. Get Netlify Blobs Credentials

1. In your Netlify dashboard, go to "Storage" → "Blobs"
2. Copy your Site ID from the URL or site settings
3. Generate a new access token in "User settings" → "Applications" → "Personal access tokens"

### 4. Update NEXTAUTH_URL

After deployment, update the `NEXTAUTH_URL` environment variable with your actual Netlify URL:
```
NEXTAUTH_URL=https://your-actual-site-name.netlify.app
```

## Features Supported

✅ **MSI File Uploads** - Full support for Windows installer files  
✅ **Large File Handling** - Up to 100MB+ with chunked uploads  
✅ **Database Operations** - All Prisma + MongoDB queries  
✅ **Authentication** - NextAuth with Google OAuth  
✅ **File Storage** - Netlify Blobs for all file types  
✅ **Node.js Runtime** - No Edge Runtime restrictions  

## Build Configuration

The build is configured via `netlify.toml`:
- **Build command**: `pnpm run build`
- **Node version**: 18
- **Function timeout**: 30 seconds (for large uploads)
- **Next.js plugin**: Automatically handles SSR and API routes

## Troubleshooting

### Build Fails
- Check environment variables are set correctly
- Ensure `DATABASE_URL` is accessible from Netlify
- Verify all required secrets are added

### File Uploads Don't Work
- Confirm `NETLIFY_SITE_ID` and `NETLIFY_TOKEN` are correct
- Check that Netlify Blobs is enabled for your site
- Verify file size limits (100MB max per file)

### Authentication Issues
- Update `NEXTAUTH_URL` to match your deployed URL
- Verify Google OAuth credentials
- Check that redirect URLs are configured in Google Console

## Performance

- **Global CDN**: Netlify's edge network for fast loading
- **Automatic HTTPS**: SSL certificates included
- **Optimized builds**: Next.js optimizations enabled
- **Function caching**: API routes cached appropriately

Your app is now ready for production on Netlify! 🚀
