services:
  - type: web
    name: devportal
    env: node
    plan: starter  # Required for large file uploads and persistent disk
    buildCommand: pnpm install --frozen-lockfile && pnpm run render-build
    startCommand: pnpm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PNPM_VERSION
        value: 8.15.6
      - key: MAX_FILE_SIZE
        value: "**********"  # 1GB in bytes
    # Auto-deploy from main branch
    autoDeploy: true
    # Health check endpoint
    healthCheckPath: /api/health
    # Disk space for uploads (only on paid plans)
    disk:
      name: uploads
      mountPath: /tmp/uploads
      sizeGB: 5  # 5GB for large file handling
