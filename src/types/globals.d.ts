// Global type declarations for Cloudflare Pages compatibility

declare global {
  // Extend global namespace to include browser globals
  namespace globalThis {
    var self: typeof globalThis | undefined
    var window: typeof globalThis | undefined
  }
  
  // Extend NodeJS global
  namespace NodeJS {
    interface Global {
      self?: typeof globalThis
      window?: typeof globalThis
    }
  }
}

export {}
