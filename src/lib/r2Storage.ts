import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

export interface R2UploadResult {
  success: boolean
  url?: string
  key?: string
  error?: string
}

class R2StorageService {
  private client: S3Client | null = null
  private bucketName: string
  private endpoint: string

  constructor() {
    this.bucketName = process.env.R2_BUCKET_NAME || 'avedevportal-files'
    this.endpoint = process.env.R2_ENDPOINT || ''
    
    if (this.isConfigured()) {
      this.client = new S3Client({
        region: 'auto',
        endpoint: this.endpoint,
        credentials: {
          accessKeyId: process.env.R2_ACCESS_KEY_ID!,
          secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
        },
      })
    }
  }

  /**
   * Check if R2 is properly configured
   */
  isConfigured(): boolean {
    return !!(
      process.env.R2_ACCESS_KEY_ID &&
      process.env.R2_SECRET_ACCESS_KEY &&
      process.env.R2_ENDPOINT &&
      process.env.R2_BUCKET_NAME
    )
  }

  /**
   * Upload a file to R2
   */
  async uploadFile(
    buffer: Buffer,
    key: string,
    contentType: string,
    metadata?: Record<string, string>
  ): Promise<R2UploadResult> {
    if (!this.client) {
      return {
        success: false,
        error: 'R2 storage not configured'
      }
    }

    try {
      console.log(`[R2] Uploading file: ${key} (${buffer.length} bytes)`)

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        Metadata: metadata,
      })

      await this.client.send(command)

      const url = `${this.endpoint}/${this.bucketName}/${key}`

      console.log(`[R2] Upload successful: ${url}`)

      return {
        success: true,
        url,
        key
      }
    } catch (error) {
      console.error('[R2] Upload failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Delete a file from R2
   */
  async deleteFile(key: string): Promise<boolean> {
    if (!this.client) {
      return false
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      })

      await this.client.send(command)
      console.log(`[R2] File deleted: ${key}`)
      return true
    } catch (error) {
      console.error('[R2] Delete failed:', error)
      return false
    }
  }

  /**
   * Get a signed URL for file access
   */
  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string | null> {
    if (!this.client) {
      return null
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      })

      const signedUrl = await getSignedUrl(this.client, command, { expiresIn })
      return signedUrl
    } catch (error) {
      console.error('[R2] Failed to generate signed URL:', error)
      return null
    }
  }

  /**
   * Generate a unique file key
   */
  generateFileKey(appId: string, fileName: string, fileType: string): string {
    const timestamp = Date.now()
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
    return `apps/${appId}/${fileType}/${timestamp}_${sanitizedFileName}`
  }
}

export const r2StorageService = new R2StorageService()
