// Polyfills for Cloudflare Pages compatibility

/**
 * Polyfill browser globals for server-side environments
 * This prevents "self is not defined" and "window is not defined" errors
 * during Cloudflare Pages build process
 */

// Use a more aggressive approach to avoid TypeScript conflicts
// This runs immediately and uses eval to bypass TypeScript checking

// Polyfill self if undefined
if (typeof self === 'undefined') {
  try {
    // Use eval to bypass TypeScript type checking
    eval('globalThis.self = globalThis')
  } catch {
    // Fallback using any casting
    ;(globalThis as any).self = globalThis
  }
}

// Polyfill window if undefined
if (typeof window === 'undefined') {
  try {
    // Use eval to bypass TypeScript type checking
    eval('globalThis.window = globalThis')
  } catch {
    // Fallback using any casting
    ;(globalThis as any).window = globalThis
  }
}

// Additional Node.js global polyfill
if (typeof global !== 'undefined') {
  if (typeof (global as any).self === 'undefined') {
    ;(global as any).self = global
  }
  if (typeof (global as any).window === 'undefined') {
    ;(global as any).window = global
  }
}

// Export empty object to make this a module
export {}
