// Polyfills for Cloudflare Pages compatibility

// Fix for "self is not defined" error - must run before any other code
if (typeof self === 'undefined') {
  (global as any).self = global
}

// Fix for "window is not defined" error
if (typeof window === 'undefined') {
  (global as any).window = global
}

// Additional polyfills for browser APIs
if (typeof globalThis !== 'undefined') {
  if (typeof globalThis.self === 'undefined') {
    globalThis.self = globalThis
  }
  if (typeof globalThis.window === 'undefined') {
    globalThis.window = globalThis as any
  }
}

// Export empty object to make this a module
export {}
