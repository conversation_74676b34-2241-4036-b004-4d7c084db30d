// Global polyfills for Node.js environment
// This fixes "self is not defined" errors during build

// Polyfill for self
if (typeof self === 'undefined') {
  if (typeof global !== 'undefined') {
    (global as any).self = global
  } else if (typeof globalThis !== 'undefined') {
    (globalThis as any).self = globalThis
  }
}

// Polyfill for window
if (typeof window === 'undefined') {
  if (typeof global !== 'undefined') {
    (global as any).window = global
  } else if (typeof globalThis !== 'undefined') {
    (globalThis as any).window = globalThis
  }
}

// Additional polyfills for browser APIs that might be referenced
if (typeof global !== 'undefined') {
  if (typeof global.document === 'undefined') {
    (global as any).document = {}
  }
  if (typeof global.navigator === 'undefined') {
    (global as any).navigator = { userAgent: 'Node.js' }
  }
}

export {}
