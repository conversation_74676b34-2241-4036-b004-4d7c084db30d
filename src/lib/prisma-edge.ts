// Edge-compatible Prisma client for Cloudflare Pages
// This extends your existing Prisma setup to work with Edge Runtime

import { PrismaClient } from '@prisma/client'
import { withAccelerate } from '@prisma/extension-accelerate'

// Create edge-compatible Prisma client
function createPrismaEdgeClient() {
  // Use Prisma Accelerate connection string if available, otherwise fall back to regular DATABASE_URL
  const databaseUrl = process.env.PRISMA_ACCELERATE_URL || process.env.DATABASE_URL

  if (!databaseUrl) {
    throw new Error('DATABASE_URL or PRISMA_ACCELERATE_URL must be defined')
  }

  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: databaseUrl,
      },
    },
    // Edge Runtime compatible configuration
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  })

  // Extend with Accelerate for Edge Runtime compatibility
  if (process.env.PRISMA_ACCELERATE_URL) {
    return prisma.$extends(withAccelerate())
  }

  return prisma
}

// Global instance for Edge Runtime
const globalForPrismaEdge = globalThis as unknown as {
  prismaEdge: ReturnType<typeof createPrismaEdgeClient> | undefined
}

// Create or reuse the edge-compatible Prisma client
export const prismaEdge = globalForPrismaEdge.prismaEdge ?? createPrismaEdgeClient()

// In development, store the client globally to prevent multiple instances
if (process.env.NODE_ENV !== 'production') {
  globalForPrismaEdge.prismaEdge = prismaEdge
}

// Helper function to determine which client to use
export function getPrismaClient() {
  // Use edge client if we're in Edge Runtime or have Accelerate URL
  if (process.env.PRISMA_ACCELERATE_URL || (typeof globalThis !== 'undefined' && 'EdgeRuntime' in globalThis)) {
    return prismaEdge
  }

  // Fall back to regular Prisma client for Node.js runtime
  const { prisma } = require('@/lib/prisma')
  return prisma
}

// Export the edge client as default for Edge Runtime routes
export default prismaEdge
