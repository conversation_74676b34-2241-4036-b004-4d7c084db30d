'use client'

export const runtime = 'edge'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { BarChart3, Clock, AlertCircle, TrendingUp } from 'lucide-react'

export default function AnalyticsPage() {
  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
            Analytics Dashboard
          </h1>
          <p className="text-zinc-400 mt-2">
            Track your app performance and user engagement
          </p>
        </div>

        {/* Not Available Message */}
        <div className="glass rounded-xl p-8 sm:p-12 border border-zinc-800/50 text-center">
          <div className="w-24 h-24 bg-gradient-to-br from-purple-500/20 to-indigo-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <BarChart3 className="w-12 h-12 text-purple-400" />
          </div>
          
          <h2 className="text-2xl font-bold text-white mb-4">
            Analytics Dashboard Not Available
          </h2>
          
          <p className="text-zinc-400 text-lg mb-6 max-w-2xl mx-auto">
            Analytics dashboard is currently not available. This feature is under development.
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-purple-400 bg-purple-500/10 border border-purple-500/20 rounded-xl px-4 py-3 max-w-md mx-auto">
            <Clock className="w-5 h-5" />
            <span className="font-medium">Coming Soon</span>
          </div>
          
          <div className="mt-8 p-4 bg-emerald-500/10 border border-emerald-500/20 rounded-xl max-w-2xl mx-auto">
            <div className="flex items-start space-x-3">
              <TrendingUp className="w-5 h-5 text-emerald-400 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h3 className="font-medium text-emerald-400 mb-1">Planned Analytics Features</h3>
                <p className="text-sm text-zinc-300">
                  Our analytics dashboard will provide comprehensive insights including:
                </p>
                <ul className="text-sm text-zinc-400 mt-2 space-y-1">
                  <li>• Download statistics and trends</li>
                  <li>• User engagement metrics</li>
                  <li>• Geographic distribution of users</li>
                  <li>• App performance analytics</li>
                  <li>• Revenue tracking (for paid apps)</li>
                  <li>• Custom date range filtering</li>
                  <li>• Exportable reports</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl max-w-2xl mx-auto">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h3 className="font-medium text-blue-400 mb-1">Stay Updated</h3>
                <p className="text-sm text-zinc-300">
                  We're working hard to bring you detailed analytics. Check back soon for updates, or enable notifications in your settings to be notified when this feature becomes available.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
