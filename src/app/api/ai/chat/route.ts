export const runtime = 'edge'

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { geminiAIService, ChatMessage } from '@/lib/ai/gemini'
import { aiTrainingService } from '@/lib/ai/training'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { messages, context } = body

    // Validate messages
    if (!Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Messages array is required' }, { status: 400 })
    }

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        return NextResponse.json({ error: 'Invalid message format' }, { status: 400 })
      }
      if (!['user', 'assistant', 'system'].includes(message.role)) {
        return NextResponse.json({ error: 'Invalid message role' }, { status: 400 })
      }
    }

    // Get database context for AI
    const databaseContext = await aiTrainingService.getDatabaseContext()

    // Add any additional context from the request
    if (context) {
      if (context.userStats) {
        Object.assign(databaseContext.userStats, context.userStats)
      }
      if (context.recentActivity) {
        databaseContext.recentActivity.unshift(...context.recentActivity)
      }
    }

    // Generate AI response
    const response = await geminiAIService.generateResponse(
      messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp || Date.now())
      })),
      databaseContext,
      session.user.id
    )

    return NextResponse.json({
      success: true,
      response
    })

  } catch (error) {
    console.error('AI chat error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET - Get AI service status and capabilities
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isConfigured = geminiAIService.isConfigured()
    const context = await aiTrainingService.getDatabaseContext()

    return NextResponse.json({
      success: true,
      status: {
        configured: isConfigured,
        available: isConfigured,
        model: 'gemini-2.0-flash-exp'
      },
      context: {
        platformStats: context.userStats,
        recentActivityCount: context.recentActivity.length,
        tablesCount: context.tables.length
      },
      capabilities: [
        'App management assistance',
        'Platform statistics and insights',
        'Development recommendations',
        'Troubleshooting support',
        'Best practices guidance',
        'Approval process help'
      ]
    })

  } catch (error) {
    console.error('AI status error:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
