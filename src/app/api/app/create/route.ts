export const maxDuration = 60

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateFile } from '@/lib/fileValidation'
import { storageService } from '@/lib/storage'
import { appNotificationService } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const apiKey = authHeader.split(' ')[1]
    if (apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!storageService.isConfigured()) {
      return NextResponse.json({ error: 'Storage service not configured' }, { status: 500 })
    }

    const formData = await request.formData()
    const name = formData.get('name') as string
    const tags = JSON.parse(formData.get('tags') as string || '[]')
    const shortDescription = formData.get('shortDescription') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string
    const version = formData.get('version') as string
    const websiteUrl = formData.get('websiteUrl') as string || null
    const supportEmail = formData.get('supportEmail') as string || null
    const minVersion = formData.get('minVersion') as string || null
    const maxVersion = formData.get('maxVersion') as string || null
    const iconFile = formData.get('icon') as File
    const appFile = formData.get('appFile') as File
    const screenshotFiles = formData.getAll('screenshots') as File[]

    if (!name || !shortDescription || !description || !category || !version) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    if (!iconFile || !appFile || screenshotFiles.length < 2) {
      return NextResponse.json({ error: 'Missing required files' }, { status: 400 })
    }

    const existingApp = await prisma.app.findFirst({
      where: { developerId: session.user.id, name: name }
    })

    if (existingApp) {
      return NextResponse.json({ error: 'App name already exists' }, { status: 400 })
    }

    const app = await prisma.app.create({
      data: {
        developerId: session.user.id,
        name,
        description,
        shortDescription,
        version,
        category,
        tags,
        downloadUrl: '',
        fileSize: appFile.size,
        screenshots: [],
        iconUrl: '',
        website: websiteUrl,
        supportEmail,
        minVersion,
        maxVersion,
        status: 'PENDING',
        pendingExpiry: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
      }
    })

    try {
      const appBuffer = Buffer.from(await appFile.arrayBuffer())
      const appValidationResult = await validateFile(appFile)
      
      if (!appValidationResult.isValid) {
        throw new Error(`App file validation failed: ${appValidationResult.errors.join(', ')}`)
      }

      const appUploadResult = await storageService.uploadFile(
        appBuffer,
        appFile.name,
        appFile.type || 'application/octet-stream',
        app.id,
        appValidationResult.fileType || 'unknown'
      )

      if (!appUploadResult.success) {
        throw new Error(`Failed to upload app file: ${appUploadResult.error}`)
      }

      const iconBuffer = Buffer.from(await iconFile.arrayBuffer())
      const iconUploadResult = await storageService.uploadFile(
        iconBuffer,
        `icon_${iconFile.name}`,
        iconFile.type || 'image/png',
        app.id,
        'image'
      )

      if (!iconUploadResult.success) {
        throw new Error(`Failed to upload icon: ${iconUploadResult.error}`)
      }

      const screenshotUrls: string[] = []
      for (let i = 0; i < screenshotFiles.length; i++) {
        const screenshot = screenshotFiles[i]
        const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())
        const screenshotUploadResult = await storageService.uploadFile(
          screenshotBuffer,
          `screenshot_${i + 1}_${screenshot.name}`,
          screenshot.type || 'image/png',
          app.id,
          'image'
        )

        if (screenshotUploadResult.success) {
          screenshotUrls.push(screenshotUploadResult.url!)
        }
      }

      await prisma.app.update({
        where: { id: app.id },
        data: {
          downloadUrl: appUploadResult.url,
          iconUrl: iconUploadResult.url,
          screenshots: screenshotUrls,
          fileType: appValidationResult.fileType,
          originalFileName: appFile.name,
          storageKey: appUploadResult.key
        }
      })

      await appNotificationService.notifyAppCreated(
        session.user.id,
        app.id,
        app.name,
        session.user.email || ''
      )

      return NextResponse.json({
        success: true,
        appId: app.id,
        message: 'App created successfully and is pending review'
      })

    } catch (uploadError) {
      await prisma.app.delete({ where: { id: app.id } }).catch(() => {})
      return NextResponse.json({
        error: 'File upload failed',
        details: uploadError instanceof Error ? uploadError.message : 'Unknown error'
      }, { status: 500 })
    }

  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
