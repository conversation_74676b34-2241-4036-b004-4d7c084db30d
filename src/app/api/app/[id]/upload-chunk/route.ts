import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateFile } from '@/lib/fileValidation'
import { storageService } from '@/lib/storage'
import { appNotificationService } from '@/lib/email'

interface ChunkUploadData {
  chunkIndex: number
  totalChunks: number
  fileName: string
  fileType: string
  totalSize: number
  chunkHash: string
}

// In-memory storage for assembling chunks
const chunkStorage = new Map<string, {
  chunks: Buffer[]
  fileName: string
  fileType: string
  totalSize: number
  totalChunks: number
}>()

// POST - Upload a file chunk

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if storage is configured
    if (!storageService.isConfigured()) {
      return NextResponse.json({ error: 'Storage service not configured' }, { status: 500 })
    }

    // Verify app exists and user owns it
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const chunk = formData.get('chunk') as File
    const metadata = JSON.parse(formData.get('metadata') as string) as ChunkUploadData

    if (!chunk) {
      return NextResponse.json({ error: 'No chunk provided' }, { status: 400 })
    }

    console.log(`[CHUNK UPLOAD] Processing chunk ${metadata.chunkIndex + 1}/${metadata.totalChunks} for app ${id}`)

    const chunkBuffer = Buffer.from(await chunk.arrayBuffer())
    const uploadKey = `${id}_${metadata.fileName}`

    // Initialize storage for first chunk
    if (metadata.chunkIndex === 0) {
      chunkStorage.set(uploadKey, {
        chunks: new Array(metadata.totalChunks),
        fileName: metadata.fileName,
        fileType: metadata.fileType,
        totalSize: metadata.totalSize,
        totalChunks: metadata.totalChunks
      })
      console.log(`[CHUNK UPLOAD] Initialized chunk storage for ${metadata.fileName}`)
    }

    // Get chunk storage info
    const uploadInfo = chunkStorage.get(uploadKey)
    if (!uploadInfo) {
      return NextResponse.json({ error: 'Upload session not found' }, { status: 400 })
    }

    // Store chunk
    uploadInfo.chunks[metadata.chunkIndex] = chunkBuffer
    console.log(`[CHUNK UPLOAD] Stored chunk ${metadata.chunkIndex + 1} (${chunkBuffer.length} bytes)`)

    // Check if all chunks are uploaded
    const isComplete = uploadInfo.chunks.every(chunk => chunk !== undefined)

    if (isComplete) {
      console.log(`[CHUNK UPLOAD] All chunks received, assembling file...`)

      // Combine all chunks
      const fullBuffer = Buffer.concat(uploadInfo.chunks)

      // Validate the assembled file
      const fileForValidation = new File([fullBuffer], metadata.fileName, { type: metadata.fileType })
      const validationResult = await validateFile(fileForValidation)

      if (!validationResult.isValid) {
        chunkStorage.delete(uploadKey)
        return NextResponse.json({
          error: 'File validation failed',
          details: validationResult.errors
        }, { status: 400 })
      }

      // Upload to storage service
      console.log(`[CHUNK UPLOAD] Uploading assembled file to storage...`)
      const uploadResult = await storageService.uploadFile(
        fullBuffer,
        metadata.fileName,
        metadata.fileType,
        id,
        validationResult.fileType || 'unknown'
      )

      if (!uploadResult.success) {
        chunkStorage.delete(uploadKey)
        return NextResponse.json({
          error: 'Failed to upload to storage',
          details: uploadResult.error
        }, { status: 500 })
      }

      // Update app in database
      await prisma.app.update({
        where: { id: id },
        data: {
          downloadUrl: uploadResult.url,
          fileSize: metadata.totalSize,
          fileType: validationResult.fileType,
          originalFileName: metadata.fileName,
          storageKey: uploadResult.key,
          pendingExpiry: existingApp.status === 'PENDING' ?
            new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) : undefined
        }
      })

      // Send notification email
      if (existingApp.status === 'PENDING') {
        const developer = await prisma.user.findUnique({
          where: { id: existingApp.developerId },
          select: { email: true, name: true }
        })

        if (developer?.email) {
          await appNotificationService.notifyAppCreated(
            existingApp.developerId,
            existingApp.id,
            existingApp.name,
            developer.email
          )
        }
      }

      // Clean up chunk storage
      chunkStorage.delete(uploadKey)

      console.log(`[CHUNK UPLOAD] File upload completed successfully: ${uploadResult.url}`)

      return NextResponse.json({
        success: true,
        completed: true,
        downloadUrl: uploadResult.url,
        fileSize: metadata.totalSize,
        fileType: validationResult.fileType
      })
    }

    // Return progress for incomplete upload
    const uploadedChunks = uploadInfo.chunks.filter(chunk => chunk !== undefined).length
    return NextResponse.json({
      success: true,
      completed: false,
      chunkIndex: metadata.chunkIndex,
      totalChunks: metadata.totalChunks,
      progress: (uploadedChunks / metadata.totalChunks) * 100
    })

  } catch (error) {
    console.error('Chunk upload error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE - Cancel upload and clean up chunks
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Get upload info from query params
    const url = new URL(request.url)
    const fileName = url.searchParams.get('fileName')

    if (!fileName) {
      return NextResponse.json({ error: 'fileName parameter required' }, { status: 400 })
    }

    const uploadKey = `${id}_${fileName}`
    const uploadInfo = chunkStorage.get(uploadKey)

    if (uploadInfo) {
      // Remove from chunk storage
      chunkStorage.delete(uploadKey)
      console.log(`[CHUNK UPLOAD] Cancelled chunk upload for ${fileName}`)
    }

    return NextResponse.json({ success: true, message: 'Upload cancelled and cleaned up' })
  } catch (error) {
    console.error('Upload cancellation error:', error)
    return NextResponse.json({ error: 'Failed to cancel upload' }, { status: 500 })
  }
}
