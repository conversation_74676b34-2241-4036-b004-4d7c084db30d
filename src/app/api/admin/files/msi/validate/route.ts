export const runtime = 'edge'

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { validateMSIFile, getMSIRecommendations } from '@/lib/msiValidation'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { prisma } = await import('@/lib/prisma')
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { admin: true }
    })

    if (!user?.admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Check if it's an MSI file
    if (!file.name.toLowerCase().endsWith('.msi')) {
      return NextResponse.json({ error: 'File must be an MSI installer' }, { status: 400 })
    }

    // Validate the MSI file
    const validationResult = await validateMSIFile(file)
    const recommendations = getMSIRecommendations(validationResult)

    return NextResponse.json({
      success: true,
      validation: validationResult,
      recommendations,
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified)
      }
    })

  } catch (error) {
    console.error('MSI validation error:', error)
    return NextResponse.json({
      error: 'MSI validation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { prisma } = await import('@/lib/prisma')
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { admin: true }
    })

    if (!user?.admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get MSI file statistics from the database
    const msiApps = await prisma.app.findMany({
      where: {
        fileType: 'msi'
      },
      select: {
        id: true,
        name: true,
        fileSize: true,
        originalFileName: true,
        createdAt: true,
        status: true,
        developer: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50
    })

    // Calculate statistics
    const totalMSIApps = msiApps.length
    const totalSize = msiApps.reduce((sum, app) => sum + (app.fileSize || 0), 0)
    const averageSize = totalMSIApps > 0 ? totalSize / totalMSIApps : 0

    const statusCounts = msiApps.reduce((counts, app) => {
      counts[app.status] = (counts[app.status] || 0) + 1
      return counts
    }, {} as Record<string, number>)

    return NextResponse.json({
      statistics: {
        totalMSIApps,
        totalSize,
        averageSize,
        statusCounts
      },
      recentMSIApps: msiApps,
      supportedFeatures: {
        metadataExtraction: true,
        digitalSignatureDetection: true,
        securityValidation: true,
        sizeOptimization: true
      }
    })

  } catch (error) {
    console.error('MSI statistics error:', error)
    return NextResponse.json({
      error: 'Failed to get MSI statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
