'use client'

export const runtime = 'edge'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Code, Clock, AlertCircle } from 'lucide-react'

export default function ApiKeysPage() {
  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
            API Keys
          </h1>
          <p className="text-zinc-400 mt-2">
            Manage your API keys for programmatic access
          </p>
        </div>

        {/* Not Available Message */}
        <div className="glass rounded-xl p-8 sm:p-12 border border-zinc-800/50 text-center">
          <div className="w-24 h-24 bg-gradient-to-br from-amber-500/20 to-orange-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Code className="w-12 h-12 text-amber-400" />
          </div>
          
          <h2 className="text-2xl font-bold text-white mb-4">
            API Keys Not Available
          </h2>
          
          <p className="text-zinc-400 text-lg mb-6 max-w-2xl mx-auto">
            API keys functionality is currently not available in our system. This feature will be coming soon.
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-amber-400 bg-amber-500/10 border border-amber-500/20 rounded-xl px-4 py-3 max-w-md mx-auto">
            <Clock className="w-5 h-5" />
            <span className="font-medium">Feature in Development</span>
          </div>
          
          <div className="mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl max-w-2xl mx-auto">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h3 className="font-medium text-blue-400 mb-1">What to expect</h3>
                <p className="text-sm text-zinc-300">
                  When available, you'll be able to generate and manage API keys for:
                </p>
                <ul className="text-sm text-zinc-400 mt-2 space-y-1">
                  <li>• Programmatic app uploads</li>
                  <li>• Automated app management</li>
                  <li>• Integration with CI/CD pipelines</li>
                  <li>• Access to analytics data</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
