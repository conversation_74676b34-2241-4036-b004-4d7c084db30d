'use client'

export const runtime = 'edge'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { FileText, Clock, AlertCircle, BookOpen, Code, Users } from 'lucide-react'

export default function DocsPage() {
  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
            Documentation
          </h1>
          <p className="text-zinc-400 mt-2">
            Guides, tutorials, and API reference for developers
          </p>
        </div>

        {/* Not Available Message */}
        <div className="glass rounded-xl p-8 sm:p-12 border border-zinc-800/50 text-center">
          <div className="w-24 h-24 bg-gradient-to-br from-emerald-500/20 to-teal-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <FileText className="w-12 h-12 text-emerald-400" />
          </div>
          
          <h2 className="text-2xl font-bold text-white mb-4">
            Documentation Not Available
          </h2>
          
          <p className="text-zinc-400 text-lg mb-6 max-w-2xl mx-auto">
            Documentation is currently not available. Please check back later for comprehensive guides and tutorials.
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-emerald-400 bg-emerald-500/10 border border-emerald-500/20 rounded-xl px-4 py-3 max-w-md mx-auto">
            <Clock className="w-5 h-5" />
            <span className="font-medium">In Development</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8 max-w-3xl mx-auto">
            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-lg mx-auto mb-3">
                <BookOpen className="w-6 h-6 text-blue-400" />
              </div>
              <h3 className="font-medium text-blue-400 mb-2">Getting Started</h3>
              <p className="text-sm text-zinc-400">
                Step-by-step guides for new developers
              </p>
            </div>
            
            <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-xl">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-500/20 rounded-lg mx-auto mb-3">
                <Code className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="font-medium text-purple-400 mb-2">API Reference</h3>
              <p className="text-sm text-zinc-400">
                Complete API documentation and examples
              </p>
            </div>
            
            <div className="p-4 bg-amber-500/10 border border-amber-500/20 rounded-xl">
              <div className="flex items-center justify-center w-12 h-12 bg-amber-500/20 rounded-lg mx-auto mb-3">
                <Users className="w-6 h-6 text-amber-400" />
              </div>
              <h3 className="font-medium text-amber-400 mb-2">Community</h3>
              <p className="text-sm text-zinc-400">
                Community guides and best practices
              </p>
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-zinc-500/10 border border-zinc-500/20 rounded-xl max-w-2xl mx-auto">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-zinc-400 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h3 className="font-medium text-zinc-300 mb-1">What's Coming</h3>
                <p className="text-sm text-zinc-400">
                  Our documentation will include:
                </p>
                <ul className="text-sm text-zinc-500 mt-2 space-y-1">
                  <li>• Quick start guides for app submission</li>
                  <li>• Detailed API documentation with code examples</li>
                  <li>• Best practices for app development</li>
                  <li>• Troubleshooting guides and FAQs</li>
                  <li>• Video tutorials and walkthroughs</li>
                  <li>• Community-contributed guides</li>
                  <li>• SDK documentation and downloads</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-zinc-500">
            <p>
              Need immediate help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
