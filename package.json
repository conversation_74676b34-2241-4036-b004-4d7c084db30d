{"name": "devportal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "pnpm exec prisma generate && next build", "start": "next start", "lint": "next lint", "db:generate": "pnpm exec prisma generate", "db:push": "pnpm exec prisma db push", "db:studio": "pnpm exec prisma studio", "db:migrate-accounts": "node scripts/fix-account-schema.js", "db:seed-email-templates": "tsx src/scripts/seed-email-templates.ts", "test:msi": "tsx src/scripts/test-msi-support.ts", "postinstall": "pnpm exec prisma generate", "render-build": "pnpm exec prisma generate && next build"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.820.0", "@aws-sdk/s3-request-presigner": "^3.820.0", "@netlify/blobs": "^9.1.3", "@prisma/client": "^6.8.2", "@types/nodemailer": "^6.4.17", "clsx": "^2.1.1", "framer-motion": "^12.15.0", "highlight.js": "^11.11.1", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "nodemailer": "^7.0.3", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sharp": "^0.34.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}