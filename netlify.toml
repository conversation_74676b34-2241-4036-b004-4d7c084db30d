[build]
  command = "pnpm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"
  PNPM_VERSION = "8.15.6"

# Next.js plugin for Netlify
[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  # Increase function timeout for large file uploads
  timeout = 30
  # Increase memory for file processing
  memory = 1024

# Headers for better performance and security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Handle large file uploads
[[headers]]
  for = "/api/app/*/upload"
  [headers.values]
    # Allow large file uploads
    X-Content-Length-Limit = "104857600"  # 100MB

[[headers]]
  for = "/api/app/*/upload-chunk"
  [headers.values]
    # Allow chunked uploads
    X-Content-Length-Limit = "10485760"   # 10MB per chunk

# Redirects for API routes
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/___netlify-handler"
  status = 200

# Handle Next.js pages
[[redirects]]
  from = "/*"
  to = "/.netlify/functions/___netlify-handler"
  status = 200
